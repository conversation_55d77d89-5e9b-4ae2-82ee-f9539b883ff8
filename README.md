# Time Tracker

A modern, cross-platform time tracking application built with Tauri v2, React, and TypeScript. Features comprehensive timer functionality, task management with hourly rates, system tray integration, and seamless Data Annotation platform integration.

## 🚀 Features

### Core Timer Functionality
- **Precision Time Tracking**: Start, stop, and manage timers with millisecond accuracy
- **Task-Based Organization**: Associate time entries with predefined tasks and hourly rates
- **Real-Time Updates**: Live timer display with automatic duration calculations
- **Persistent State**: Timers continue running even when the app is closed

### System Tray Integration
- **Cross-Platform Support**: Native system tray for both macOS and Windows
- **Real-Time Display**: Live timer duration shown in tray title (macOS) or tooltip
- **Quick Actions**: Start/stop timers directly from the system tray
- **Task Selection**: Quick access to recent tasks for instant timer start
- **Daily Totals**: View total time tracked per day from the tray

### Task Management
- **Predefined Tasks**: Create and manage tasks with custom hourly rates
- **Earnings Calculation**: Automatic calculation of earnings based on time and rates
- **Task Categories**: Organize tasks with descriptions and metadata
- **Quick Task Creation**: Create new tasks on-the-fly during timer operations

### Data Annotation Integration
- **Webview Integration**: Seamless access to Data Annotation platform via secure webview
- **IPC Communication**: Real-time data transfer using Tauri v2's event system
- **Payment Tracking**: Extract and track payment data from Data Annotation
- **Secure Access**: Restricted webview with CSP-compliant security policies

### Templated Notes System
- **Template Management**: Create and manage note templates with custom fields
- **Drag-and-Drop Builder**: Visual field builder for template creation
- **Auto-Save**: Automatic saving of notes with collision detection
- **Task Integration**: Link notes to specific tasks and time entries

### Data Management
- **Local Storage**: All data stored locally with automatic backup
- **JSON Export**: Export time entries, tasks, and templates to JSON format
- **Data Backup**: Comprehensive backup system with metadata
- **Import/Export**: Full data portability with version control

## 🛠 Technology Stack

- **Frontend**: React 18, TypeScript, Material-UI v7
- **Backend**: Rust with Tauri v2 framework
- **Build Tool**: Vite with React plugin
- **Testing**: Jest with React Testing Library
- **State Management**: React hooks with local storage persistence
- **UI Components**: Material-UI with dark theme support
- **System Integration**: Native system tray, IPC communication
- **Security**: Content Security Policy, secure webview implementation

## 📋 Prerequisites

- **Node.js**: Version 18.0 or higher
- **Rust**: Latest stable version (1.70+)
- **Platform Requirements**:
  - **macOS**: macOS 10.15 or later
  - **Windows**: Windows 10 or later
- **Development Tools**: Git, npm/yarn/pnpm

## 🔧 Installation

### 1. Clone the Repository
```bash
git clone <repository-url>
cd time-tracker
```

### 2. Install Dependencies
```bash
# Install Node.js dependencies
npm install

# Install Rust dependencies (handled automatically by Tauri)
```

### 3. Development Setup
```bash
# Start development server
npm run dev

# This will:
# - Start Vite dev server on http://localhost:1420
# - Launch Tauri development window
# - Enable hot reload for both frontend and backend
```

## 🚀 Development

### Available Scripts

```bash
# Development
npm run dev          # Start development server with hot reload
npm run build        # Build for production
npm run preview      # Preview production build

# Tauri Commands
npm run tauri dev    # Start Tauri development mode
npm run tauri build  # Build production executable

# Testing
npm run test         # Run all tests
npm run test:watch   # Run tests in watch mode
npm run test:coverage # Run tests with coverage report
npm run test:unit    # Run unit tests only
npm run test:integration # Run integration tests only
```

### Project Structure

```
time-tracker/
├── src/                    # React frontend source
│   ├── components/         # React components
│   ├── hooks/             # Custom React hooks
│   ├── services/          # Business logic services
│   ├── types/             # TypeScript type definitions
│   ├── contexts/          # React contexts (Theme, etc.)
│   └── __tests__/         # Test files
├── src-tauri/             # Rust backend source
│   ├── src/               # Rust source code
│   ├── icons/             # Application icons
│   ├── capabilities/      # Tauri security capabilities
│   └── Cargo.toml         # Rust dependencies
├── public/                # Static assets
└── dist/                  # Built frontend (generated)
```

## 🎯 Usage Guide

### Basic Timer Operations
1. **Start Timer**: Select a task and click "Start Timer"
2. **Stop Timer**: Click "Stop Timer" or use system tray
3. **View Entries**: Check the calendar view for all time entries
4. **Edit Entries**: Click on any entry to modify details

### Task Management
1. **Create Tasks**: Go to Task Management tab
2. **Set Hourly Rates**: Configure rates for automatic earnings calculation
3. **Quick Start**: Use system tray for instant task selection

### System Tray Features
- **Left Click**: Show/hide main application window
- **Right Click**: Access context menu with timer controls
- **Real-Time Display**: See current timer duration in tray

### Data Annotation Integration
1. **Access**: Click "DataAnnotation" in navigation
2. **Authentication**: Login through secure webview
3. **Data Sync**: Payment data automatically syncs to main app
